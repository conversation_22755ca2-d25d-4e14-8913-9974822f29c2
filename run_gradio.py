#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动Gradio前端应用的脚本
"""

import sys
import os
import argparse
from loguru import logger
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
if os.path.exists(env_file):
    load_dotenv(env_file)
    logger.info(f"已加载环境配置: {env_file}")
else:
    # 如果特定环境的配置文件不存在，尝试加载默认的.env文件
    if os.path.exists(".env"):
        load_dotenv(".env")
        logger.info("已加载默认环境配置: .env")
    else:
        logger.warning(f"未找到环境配置文件: {env_file} 或 .env")

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from frontend.gradio_app_v2 import create_gradio_interface



def main():
    parser = argparse.ArgumentParser(description="启动Gradio前端应用")
    parser.add_argument(
        "--host", 
        type=str, 
        default="0.0.0.0", 
        help="服务器主机地址，默认为0.0.0.0"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=7862,
        help="服务器端口，默认为7862"
    )
    parser.add_argument(
        "--share", 
        action="store_true", 
        help="是否创建公共链接"
    )
    parser.add_argument(
        "--debug", 
        action="store_true", 
        help="是否启用调试模式"
    )

    
    args = parser.parse_args()
    
    print(f"启动Gradio前端应用...")
    print(f"服务器地址: {args.host}:{args.port}")
    print(f"公共链接: {'是' if args.share else '否'}")
    print(f"调试模式: {'是' if args.debug else '否'}")
    logger.info(f"启动Gradio前端应用，地址: {args.host}:{args.port}，公共链接: {'是' if args.share else '否'}，调试: {'是' if args.debug else '否'}")
    
    # 创建并启动界面
    interface = create_gradio_interface()
    interface.launch(
        server_name=args.host,
        server_port=args.port,
        share=args.share,
        debug=args.debug,
    )

if __name__ == "__main__":
    main()