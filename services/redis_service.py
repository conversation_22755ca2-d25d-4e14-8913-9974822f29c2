
import json
import httpx
import logging
from config.doc_redis_config import DOC_REDIS_CONFIG
from loguru import logger
from config.logging_config import configure_logging
configure_logging()


class RedisService():
    def __init__(self, config: dict = None, request_id: str = None):
        self.config = config or DOC_REDIS_CONFIG
        self.api_url = self.config["api_url"]
        self.logger = logger.bind(request_id=request_id)

    async def search(self, conversation_id: str, **kwargs):

        payload = {
            "conversationId": conversation_id,
        }
        self.logger.info(f"检索请求参数: {self.api_url}/conversation/attachment, 参数: {payload}")
        try:
            async with httpx.AsyncClient(timeout=60) as client:
                response = await client.post(
                    f"{self.api_url}/conversation/attachment",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
            self.logger.info(f"检索响应: {response.status_code}")
            # print(f"{response.status_code} - {response.text}")
            if response.status_code == 200:
                result = response.json()
                search_results = result.get('data')
                return search_results, None
            else:
                self.logger.error(f"检索API请求失败: {response.status_code} - {response.text}")
                return None, f"API请求失败: {response.status_code} - {response.text}"
        except Exception as e:
            return None, str(e)


if __name__ == "__main__":
    redis_service = RedisService(config={"api_url": "http://quickdb.test.b2c.srv/ai"}, request_id="doc_qa_single")
    result = redis_service.search(conversation_id="test-123")
    print(result)