#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块测试脚本
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """测试所有模块导入"""
    print("开始测试模块导入...")
    
    try:
        # 测试配置模块
        from frontend.modules.config import API_BASE_URL, headers
        print("✓ 配置模块导入成功")
        
        # 测试工具模块
        from frontend.modules.utils.logging_utils import setup_logger
        from frontend.modules.utils.formatters import format_reference_display
        from frontend.modules.utils.timing import TimingManager
        print("✓ 工具模块导入成功")
        
        # 测试API模块
        from frontend.modules.api.chat_client import ChatClient
        from frontend.modules.api.stream_handlers import LLMStreamHandler, RAGStreamHandler
        from frontend.modules.api.extended_handlers import CarStreamHandler, AllQAStreamHandler
        from frontend.modules.api.sync_wrappers import create_sync_wrapper
        print("✓ API模块导入成功")
        
        # 测试样式模块
        from frontend.modules.styles.css_styles import get_custom_css
        from frontend.modules.styles.js_scripts import get_custom_js
        print("✓ 样式模块导入成功")
        
        # 测试UI模块
        from frontend.modules.ui.components import create_config_sidebar
        from frontend.modules.ui.layouts import create_llm_layout
        from frontend.modules.ui.tabs import create_main_tabs
        print("✓ UI模块导入成功")
        
        # 测试主应用模块
        from frontend.modules.app import GradioApp, create_gradio_interface
        print("✓ 主应用模块导入成功")
        
        print("\n所有模块导入测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n开始测试基本功能...")
    
    try:
        # 测试ChatClient
        from frontend.modules.api.chat_client import ChatClient
        client = ChatClient()
        client.clear_history("all")
        print("✓ ChatClient基本功能正常")
        
        # 测试TimingManager
        from frontend.modules.utils.timing import TimingManager
        timer = TimingManager()
        timer.start_request()
        timer.mark_reasoning_start()
        times = timer.get_elapsed_times()
        print("✓ TimingManager基本功能正常")
        
        # 测试格式化工具
        from frontend.modules.utils.formatters import format_reference_display
        result = format_reference_display("")
        print("✓ 格式化工具基本功能正常")
        
        # 测试样式获取
        from frontend.modules.styles.css_styles import get_custom_css
        from frontend.modules.styles.js_scripts import get_custom_js
        css = get_custom_css()
        js = get_custom_js()
        assert len(css) > 0 and len(js) > 0
        print("✓ 样式获取功能正常")
        
        print("\n基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def test_gradio_interface():
    """测试Gradio界面创建"""
    print("\n开始测试Gradio界面创建...")
    
    try:
        from frontend.modules.app import create_gradio_interface
        interface = create_gradio_interface()
        print("✓ Gradio界面创建成功")
        return True
        
    except Exception as e:
        print(f"✗ Gradio界面创建失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("前端模块化架构测试")
    print("=" * 50)
    
    success = True
    
    # 运行所有测试
    success &= test_imports()
    success &= test_basic_functionality()
    success &= test_gradio_interface()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！模块化重构成功！")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    print("=" * 50)
