from typing import List, Dict, Any, AsyncGenerator, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from prompts.data_qa_prompt import data_qa_sys_prompt, data_qa_user_prompt, data_qa_rerank_prompt
from services.search_service import SearchService
from services.rerank_service import RerankService

from loguru import logger
from config.logging_config import configure_logging
from config.all_search_config import ALL_SEARCH_COLLECTIONS, ALL_SEARCH_MODEL_CONFIG, ALL_SEARCH_RERANK_CONFIG, DATA_SEARCH_COLLECTIONS, HARDWARE_SEARCH_COLLECTIONS
configure_logging()

import json
import asyncio

class SEARCH:
    """RAG问答类，支持流式和非流式输出"""
    
    def __init__(self, request_id: str = None):
        """
        初始化RAG问答实例
        
        Args:
            request_id: 可选请求ID
        """
        self.request_id = request_id
        self.search_service = SearchService(config = ALL_SEARCH_MODEL_CONFIG, request_id=request_id)
        self.rerank_service = RerankService(config = ALL_SEARCH_RERANK_CONFIG, request_id=request_id)
        self.logger = logger.bind(request_id=request_id)
    
    async def _retrieve_knowledge(self, query: str, user_id: str, collection_name: str, top_k: int = 60, min_score: float = None):
        """检索单个库并重排，不做top_r过滤"""
        self.logger.info(f"检索库: {collection_name}, 用户ID: {user_id}, 查询: {query}, top_k: {top_k}, min_score: {min_score}")
        search_results, error = await self.search_service.search(
            user_id=user_id,
            query=query,
            top_k=top_k,
            collection_name=collection_name
        )
        if error or not search_results:
            return []
        self.logger.info(f"库 {collection_name} 检索到知识: {len(search_results)} 条")
        # print(f"检索到的知识: {search_results}")
        # 重排知识 - 使用DATAQA专门的rerank prompt
        reranked_docs = await self.rerank_service.rerank_with_prompt(
            query=query,
            documents=search_results,
            instruction=data_qa_rerank_prompt,
            top_r=top_k,
            min_score=min_score
        )
        self.logger.info(f"库 {collection_name} 重排后知识: {len(reranked_docs)} 条")
        return reranked_docs

    async def _retrieve_and_rerank_all_collections(self, query: str, user_id: str, collections, top_k: int = 20, top_r: int = None, min_score: float = None):
        """异步检索指定collections并重排，按分组返回每组前20条"""
        tasks = []
        collection_names = []
        for collection in collections:
            collection_name = collection.get("collection_name")
            if collection_name:
                tasks.append(self._retrieve_knowledge(query, user_id, collection_name, top_k=top_k, min_score=min_score))
                collection_names.append(collection_name)
        all_results = await asyncio.gather(*tasks)
        # 合并结果，分组
        data_collections = set([c["collection_name"] for c in DATA_SEARCH_COLLECTIONS])
        search_collections = set([c["collection_name"] for c in HARDWARE_SEARCH_COLLECTIONS])
        data_group = []
        search_group = []
        for collection_name, docs in zip(collection_names, all_results):
            if collection_name in data_collections:
                data_group.extend(docs)
            elif collection_name in search_collections:
                search_group.extend(docs)
        # 按score排序，每组取前20
        data_group.sort(key=lambda x: x.get("score", 0), reverse=True)
        search_group.sort(key=lambda x: x.get("score", 0), reverse=True)
        
        res = [{
            "collection": "hardwareKnowledge","refs": search_group[:20] 
        },
        {
            "collection": "rDataQuery","refs": data_group[:20]
        }
        ]
        
        return res

    def format_knowledge(self, reranked_docs):
        """格式化重排后的知识为字符串"""
        formatted_docs = []
        for i, doc in enumerate(reranked_docs):
            formatted_doc = f"\n检索结果{i+1}:\n"
            formatted_doc += f"{doc.get('content', '')}\n"
            formatted_docs.append(formatted_doc)
        self.logger.info(f"格式化后的知识, 共: {len(formatted_docs)}条")
        return "\n\n".join(formatted_docs), reranked_docs
    
    def deduplicate_by_docurl(self,docs):
        seen = set()
        deduped = []
        for doc in docs:
            url = doc.get("docUrl")
            if url not in seen:
                deduped.append(doc)
                seen.add(url)
        return deduped
    
    async def search_all_collections(
        self, 
        query: str, 
        user_id: str, 
        timeout: Optional[float] = None,
        top_k: int = 60,
        top_r: int = None,
        min_score: float = None,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        检索内容
        """
        # 检索所有库并重排，分组
        grouped_res = await self._retrieve_and_rerank_all_collections(query, user_id, ALL_SEARCH_COLLECTIONS, top_k=top_k, top_r=top_r, min_score=min_score)
        self.logger.info(f"检索完成")
        yield grouped_res